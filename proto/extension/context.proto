// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

syntax = "proto3";

package envoygateway.extension;

option go_package = "proto/extension";


// RouteExtensionContext provides resources introduced by an extension and watched by Envoy Gateway
// additional context information can be added to this message as more use-cases are discovered
message PostRouteExtensionContext {
    // Resources introduced by the extension that were used as extensionRefs in an HTTPRoute/GRPCRoute
    repeated ExtensionResource extension_resources = 1;

    // hostnames are the fully qualified domain names attached to the HTTPRoute
    repeated string hostnames = 2;
}


// PostVirtualHostExtensionContext provides context information for VirtualHost modification
// additional context information can be added to this message as more use-cases are discovered
message PostVirtualHostExtensionContext {
    // Resources introduced by the extension that were used as custom backendRefs
    repeated ExtensionResource backend_extension_resources = 1;
}


// PostClusterExtensionContext provides context information for cluster modification
// additional context information can be added to this message as more use-cases are discovered
message PostClusterExtensionContext {
    // Resources introduced by the extension that were used as custom backendRefs
    repeated ExtensionResource backend_extension_resources = 1;
}


// Empty for now but we can add fields to the context as use-cases are discovered without
// breaking any clients that use the API
// additional context information can be added to this message as more use-cases are discovered
message PostHTTPListenerExtensionContext {
    // Resources introduced by the extension that were used as extension server
    // policies targeting the listener
    repeated ExtensionResource extension_resources = 1;
    // Resources introduced by the extension that were used as custom backendRefs
    repeated ExtensionResource backend_extension_resources = 2;
}


// Empty for now but we can add fields to the context as use-cases are discovered without
// breaking any clients that use the API
// additional context information can be added to this message as more use-cases are discovered
message PostTranslateExtensionContext {
    // Resources/Policies introduced by the extension that were used as extension server
    // policies targeting the clusters
    repeated ExtensionResource extension_resources = 1;
}


// ExtensionResource stores the data for a K8s API object referenced in an HTTPRouteFilter
// extensionRef. It is constructed from an unstructured.Unstructured marshalled to JSON. An extension
// can marshal the bytes from this resource back into an unstructured.Unstructured and then 
// perform type checking to obtain the resource.
message ExtensionResource {
    bytes unstructured_bytes = 1;
}

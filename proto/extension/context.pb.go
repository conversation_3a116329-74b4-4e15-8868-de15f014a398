// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        (unknown)
// source: proto/extension/context.proto

package extension

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RouteExtensionContext provides resources introduced by an extension and watched by Envoy Gateway
// additional context information can be added to this message as more use-cases are discovered
type PostRouteExtensionContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Resources introduced by the extension that were used as extensionRefs in an HTTPRoute/GRPCRoute
	ExtensionResources []*ExtensionResource `protobuf:"bytes,1,rep,name=extension_resources,json=extensionResources,proto3" json:"extension_resources,omitempty"`
	// hostnames are the fully qualified domain names attached to the HTTPRoute
	Hostnames     []string `protobuf:"bytes,2,rep,name=hostnames,proto3" json:"hostnames,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostRouteExtensionContext) Reset() {
	*x = PostRouteExtensionContext{}
	mi := &file_proto_extension_context_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostRouteExtensionContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostRouteExtensionContext) ProtoMessage() {}

func (x *PostRouteExtensionContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostRouteExtensionContext.ProtoReflect.Descriptor instead.
func (*PostRouteExtensionContext) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{0}
}

func (x *PostRouteExtensionContext) GetExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.ExtensionResources
	}
	return nil
}

func (x *PostRouteExtensionContext) GetHostnames() []string {
	if x != nil {
		return x.Hostnames
	}
	return nil
}

// PostVirtualHostExtensionContext provides context information for VirtualHost modification
// additional context information can be added to this message as more use-cases are discovered
type PostVirtualHostExtensionContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Resources introduced by the extension that were used as custom backendRefs
	BackendExtensionResources []*ExtensionResource `protobuf:"bytes,1,rep,name=backend_extension_resources,json=backendExtensionResources,proto3" json:"backend_extension_resources,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *PostVirtualHostExtensionContext) Reset() {
	*x = PostVirtualHostExtensionContext{}
	mi := &file_proto_extension_context_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostVirtualHostExtensionContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostVirtualHostExtensionContext) ProtoMessage() {}

func (x *PostVirtualHostExtensionContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostVirtualHostExtensionContext.ProtoReflect.Descriptor instead.
func (*PostVirtualHostExtensionContext) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{1}
}

func (x *PostVirtualHostExtensionContext) GetBackendExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.BackendExtensionResources
	}
	return nil
}

// PostClusterExtensionContext provides context information for cluster modification
// additional context information can be added to this message as more use-cases are discovered
type PostClusterExtensionContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Resources introduced by the extension that were used as custom backendRefs
	BackendExtensionResources []*ExtensionResource `protobuf:"bytes,1,rep,name=backend_extension_resources,json=backendExtensionResources,proto3" json:"backend_extension_resources,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *PostClusterExtensionContext) Reset() {
	*x = PostClusterExtensionContext{}
	mi := &file_proto_extension_context_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostClusterExtensionContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostClusterExtensionContext) ProtoMessage() {}

func (x *PostClusterExtensionContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostClusterExtensionContext.ProtoReflect.Descriptor instead.
func (*PostClusterExtensionContext) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{2}
}

func (x *PostClusterExtensionContext) GetBackendExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.BackendExtensionResources
	}
	return nil
}

// Empty for now but we can add fields to the context as use-cases are discovered without
// breaking any clients that use the API
// additional context information can be added to this message as more use-cases are discovered
type PostHTTPListenerExtensionContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Resources introduced by the extension that were used as extension server
	// policies targeting the listener
	ExtensionResources []*ExtensionResource `protobuf:"bytes,1,rep,name=extension_resources,json=extensionResources,proto3" json:"extension_resources,omitempty"`
	// Resources introduced by the extension that were used as custom backendRefs
	BackendExtensionResources []*ExtensionResource `protobuf:"bytes,2,rep,name=backend_extension_resources,json=backendExtensionResources,proto3" json:"backend_extension_resources,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *PostHTTPListenerExtensionContext) Reset() {
	*x = PostHTTPListenerExtensionContext{}
	mi := &file_proto_extension_context_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostHTTPListenerExtensionContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostHTTPListenerExtensionContext) ProtoMessage() {}

func (x *PostHTTPListenerExtensionContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostHTTPListenerExtensionContext.ProtoReflect.Descriptor instead.
func (*PostHTTPListenerExtensionContext) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{3}
}

func (x *PostHTTPListenerExtensionContext) GetExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.ExtensionResources
	}
	return nil
}

func (x *PostHTTPListenerExtensionContext) GetBackendExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.BackendExtensionResources
	}
	return nil
}

// Empty for now but we can add fields to the context as use-cases are discovered without
// breaking any clients that use the API
// additional context information can be added to this message as more use-cases are discovered
type PostTranslateExtensionContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Resources/Policies introduced by the extension that were used as extension server
	// policies targeting the clusters
	ExtensionResources []*ExtensionResource `protobuf:"bytes,1,rep,name=extension_resources,json=extensionResources,proto3" json:"extension_resources,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PostTranslateExtensionContext) Reset() {
	*x = PostTranslateExtensionContext{}
	mi := &file_proto_extension_context_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostTranslateExtensionContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostTranslateExtensionContext) ProtoMessage() {}

func (x *PostTranslateExtensionContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostTranslateExtensionContext.ProtoReflect.Descriptor instead.
func (*PostTranslateExtensionContext) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{4}
}

func (x *PostTranslateExtensionContext) GetExtensionResources() []*ExtensionResource {
	if x != nil {
		return x.ExtensionResources
	}
	return nil
}

// ExtensionResource stores the data for a K8s API object referenced in an HTTPRouteFilter
// extensionRef. It is constructed from an unstructured.Unstructured marshalled to JSON. An extension
// can marshal the bytes from this resource back into an unstructured.Unstructured and then
// perform type checking to obtain the resource.
type ExtensionResource struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UnstructuredBytes []byte                 `protobuf:"bytes,1,opt,name=unstructured_bytes,json=unstructuredBytes,proto3" json:"unstructured_bytes,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ExtensionResource) Reset() {
	*x = ExtensionResource{}
	mi := &file_proto_extension_context_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtensionResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionResource) ProtoMessage() {}

func (x *ExtensionResource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_extension_context_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionResource.ProtoReflect.Descriptor instead.
func (*ExtensionResource) Descriptor() ([]byte, []int) {
	return file_proto_extension_context_proto_rawDescGZIP(), []int{5}
}

func (x *ExtensionResource) GetUnstructuredBytes() []byte {
	if x != nil {
		return x.UnstructuredBytes
	}
	return nil
}

var File_proto_extension_context_proto protoreflect.FileDescriptor

var file_proto_extension_context_proto_rawDesc = string([]byte{
	0x0a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x95, 0x01, 0x0a, 0x19, 0x50, 0x6f, 0x73, 0x74,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x5a, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x12, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22,
	0x8c, 0x01, 0x0a, 0x1f, 0x50, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x48,
	0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x69, 0x0a, 0x1b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x19, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x88,
	0x01, 0x0a, 0x1b, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x69,
	0x0a, 0x1b, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x19,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x20, 0x50, 0x6f,
	0x73, 0x74, 0x48, 0x54, 0x54, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x5a,
	0x0a, 0x13, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e,
	0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x12, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x1b, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x19, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x7b, 0x0a, 0x1d, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x5a, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x12,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x22, 0x42, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x75, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x11, 0x75, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x42, 0x11, 0x5a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_proto_extension_context_proto_rawDescOnce sync.Once
	file_proto_extension_context_proto_rawDescData []byte
)

func file_proto_extension_context_proto_rawDescGZIP() []byte {
	file_proto_extension_context_proto_rawDescOnce.Do(func() {
		file_proto_extension_context_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_extension_context_proto_rawDesc), len(file_proto_extension_context_proto_rawDesc)))
	})
	return file_proto_extension_context_proto_rawDescData
}

var file_proto_extension_context_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_extension_context_proto_goTypes = []any{
	(*PostRouteExtensionContext)(nil),        // 0: envoygateway.extension.PostRouteExtensionContext
	(*PostVirtualHostExtensionContext)(nil),  // 1: envoygateway.extension.PostVirtualHostExtensionContext
	(*PostClusterExtensionContext)(nil),      // 2: envoygateway.extension.PostClusterExtensionContext
	(*PostHTTPListenerExtensionContext)(nil), // 3: envoygateway.extension.PostHTTPListenerExtensionContext
	(*PostTranslateExtensionContext)(nil),    // 4: envoygateway.extension.PostTranslateExtensionContext
	(*ExtensionResource)(nil),                // 5: envoygateway.extension.ExtensionResource
}
var file_proto_extension_context_proto_depIdxs = []int32{
	5, // 0: envoygateway.extension.PostRouteExtensionContext.extension_resources:type_name -> envoygateway.extension.ExtensionResource
	5, // 1: envoygateway.extension.PostVirtualHostExtensionContext.backend_extension_resources:type_name -> envoygateway.extension.ExtensionResource
	5, // 2: envoygateway.extension.PostClusterExtensionContext.backend_extension_resources:type_name -> envoygateway.extension.ExtensionResource
	5, // 3: envoygateway.extension.PostHTTPListenerExtensionContext.extension_resources:type_name -> envoygateway.extension.ExtensionResource
	5, // 4: envoygateway.extension.PostHTTPListenerExtensionContext.backend_extension_resources:type_name -> envoygateway.extension.ExtensionResource
	5, // 5: envoygateway.extension.PostTranslateExtensionContext.extension_resources:type_name -> envoygateway.extension.ExtensionResource
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_proto_extension_context_proto_init() }
func file_proto_extension_context_proto_init() {
	if File_proto_extension_context_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_extension_context_proto_rawDesc), len(file_proto_extension_context_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_extension_context_proto_goTypes,
		DependencyIndexes: file_proto_extension_context_proto_depIdxs,
		MessageInfos:      file_proto_extension_context_proto_msgTypes,
	}.Build()
	File_proto_extension_context_proto = out.File
	file_proto_extension_context_proto_goTypes = nil
	file_proto_extension_context_proto_depIdxs = nil
}

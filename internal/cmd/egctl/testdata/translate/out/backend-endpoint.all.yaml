backendTLSPolicies:
- apiVersion: gateway.networking.k8s.io/v1alpha3
  kind: BackendTLSPolicy
  metadata:
    creationTimestamp: null
    name: example-com-tls-policy
    namespace: envoy-gateway-system
  spec:
    targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend
    validation:
      hostname: www.example.com
      wellKnownCACertificates: System
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
backends:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend
    namespace: envoy-gateway-system
  spec:
    endpoints:
    - ip:
        address: 0.0.0.0
        port: 3000
    type: Endpoints
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: ext-proc-example
    namespace: envoy-gateway-system
  spec:
    extProc:
    - backendRefs:
      - group: ""
        kind: Service
        name: grpc-ext-proc
        port: 9002
      failOpen: false
      processingMode:
        request: {}
        response:
          body: Streamed
    targetRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: backend
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
        namespace: envoy-gateway-system
      conditions:
      - lastTransitionTime: null
        message: 'ExtProc: service envoy-gateway-system/grpc-ext-proc not found.'
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gatewayClass:
  apiVersion: gateway.networking.k8s.io/v1
  kind: GatewayClass
  metadata:
    creationTimestamp: null
    name: eg
  spec:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
  status:
    conditions:
    - lastTransitionTime: null
      message: Valid GatewayClass
      reason: Accepted
      status: "True"
      type: Accepted
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: eg
    namespace: envoy-gateway-system
  spec:
    gatewayClassName: eg
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: envoy-gateway-system
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend
        weight: 1
      matches:
      - path:
          type: PathPrefix
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg

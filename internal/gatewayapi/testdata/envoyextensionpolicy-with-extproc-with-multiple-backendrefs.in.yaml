gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: default
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - www.foo.com
    parentRefs:
    - namespace: default
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: /foo
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-2
  spec:
    hostnames:
    - www.bar.com
    parentRefs:
    - namespace: default
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: /bar
      backendRefs:
      - name: service-1
        port: 8080
services:
- apiVersion: v1
  kind: Service
  metadata:
    namespace: envoy-gateway
    name: grpc-backend
  spec:
    ports:
    - port: 8000
      name: grpc
      protocol: TCP
serviceImports:
- apiVersion: multicluster.x-k8s.io/v1alpha1
  kind: ServiceImport
  metadata:
    namespace: default
    name: grpc-backend-2
  spec:
    ips:
    - *******
    ports:
    - port: 9000
      name: grpc
      protocol: TCP
endpointSlices:
- apiVersion: discovery.k8s.io/v1
  kind: EndpointSlice
  metadata:
    name: endpointslice-grpc-backend
    namespace: envoy-gateway
    labels:
      kubernetes.io/service-name: grpc-backend
  addressType: IPv4
  ports:
  - name: http
    protocol: TCP
    port: 8000
  endpoints:
  - addresses:
    - *******
    conditions:
      ready: true
- apiVersion: discovery.k8s.io/v1
  kind: EndpointSlice
  metadata:
    name: endpointslice-grpc-backend-2
    namespace: default
    labels:
      multicluster.kubernetes.io/service-name: grpc-backend-2
  addressType: IPv4
  ports:
  - name: grpc
    protocol: TCP
    port: 9000
  endpoints:
  - addresses:
    - *******
    conditions:
      ready: true
referenceGrants:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: ReferenceGrant
  metadata:
    namespace: envoy-gateway
    name: referencegrant-1
  spec:
    from:
    - group: gateway.envoyproxy.io
      kind: EnvoyExtensionPolicy
      namespace: default
    to:
    - group: ''
      kind: Service
    - group: gateway.envoyproxy.io
      kind: Backend
configMaps:
- apiVersion: v1
  kind: ConfigMap
  metadata:
    name: ca-cmap
    namespace: envoy-gateway
  data:
    ca.crt: |
      -----BEGIN CERTIFICATE-----
      MIIDJzCCAg+gAwIBAgIUAl6UKIuKmzte81cllz5PfdN2IlIwDQYJKoZIhvcNAQEL
      BQAwIzEQMA4GA1UEAwwHbXljaWVudDEPMA0GA1UECgwGa3ViZWRiMB4XDTIzMTAw
      MjA1NDE1N1oXDTI0MTAwMTA1NDE1N1owIzEQMA4GA1UEAwwHbXljaWVudDEPMA0G
      A1UECgwGa3ViZWRiMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwSTc
      1yj8HW62nynkFbXo4VXKv2jC0PM7dPVky87FweZcTKLoWQVPQE2p2kLDK6OEszmM
      yyr+xxWtyiveremrWqnKkNTYhLfYPhgQkczib7eUalmFjUbhWdLvHakbEgCodn3b
      kz57mInX2VpiDOKg4kyHfiuXWpiBqrCx0KNLpxo3DEQcFcsQTeTHzh4752GV04RU
      Ti/GEWyzIsl4Rg7tGtAwmcIPgUNUfY2Q390FGqdH4ahn+mw/6aFbW31W63d9YJVq
      ioyOVcaMIpM5B/c7Qc8SuhCI1YGhUyg4cRHLEw5VtikioyE3X04kna3jQAj54YbR
      bpEhc35apKLB21HOUQIDAQABo1MwUTAdBgNVHQ4EFgQUyvl0VI5vJVSuYFXu7B48
      6PbMEAowHwYDVR0jBBgwFoAUyvl0VI5vJVSuYFXu7B486PbMEAowDwYDVR0TAQH/
      BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAMLxrgFVMuNRq2wAwcBt7SnNR5Cfz
      2MvXq5EUmuawIUi9kaYjwdViDREGSjk7JW17vl576HjDkdfRwi4E28SydRInZf6J
      i8HZcZ7caH6DxR335fgHVzLi5NiTce/OjNBQzQ2MJXVDd8DBmG5fyatJiOJQ4bWE
      A7FlP0RdP3CO3GWE0M5iXOB2m1qWkE2eyO4UHvwTqNQLdrdAXgDQlbam9e4BG3Gg
      d/6thAkWDbt/QNT+EJHDCvhDRKh1RuGHyg+Y+/nebTWWrFWsktRrbOoHCZiCpXI1
      3eXE6nt0YkgtDxG22KqnhpAg9gUSs2hlhoxyvkzyF0mu6NhPlwAgnq7+/Q==
      -----END CERTIFICATE-----
backendTLSPolicies:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: BackendTLSPolicy
  metadata:
    name: policy-btls-grpc
    namespace: envoy-gateway
  spec:
    targetRefs:
    - group: ''
      kind: Service
      name: grpc-backend
      sectionName: grpc
    validation:
      caCertificateRefs:
      - name: ca-cmap
        group: ''
        kind: ConfigMap
      hostname: grpc-backend
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: BackendTLSPolicy
  metadata:
    name: policy-btls-backend-ip
    namespace: envoy-gateway
  spec:
    targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-ip-tls
    validation:
      caCertificateRefs:
      - name: ca-cmap
        group: ''
        kind: ConfigMap
      hostname: ip-backend
envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: default
    name: policy-for-http-route
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    extProc:
    - backendRefs:
      - Name: grpc-backend
        Namespace: envoy-gateway
        Port: 8000
      - group: multicluster.x-k8s.io
        kind: ServiceImport
        name: grpc-backend-2
        port: 9000
      - Name: backend-ip
        Kind: Backend
        Group: gateway.envoyproxy.io
      - Name: backend-ip-tls
        Namespace: envoy-gateway
        Kind: Backend
        Group: gateway.envoyproxy.io
backends:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    name: backend-ip
    namespace: default
  spec:
    endpoints:
    - ip:
        address: *******
        port: 3001
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    name: backend-ip-tls
    namespace: envoy-gateway
  spec:
    endpoints:
    - ip:
        address: *******
        port: 3443

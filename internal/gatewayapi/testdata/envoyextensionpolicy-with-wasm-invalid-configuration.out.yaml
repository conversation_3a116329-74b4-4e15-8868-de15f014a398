envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-httproute-1
    namespace: default
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: 'Wasm: secret default/my-pull-secret-1 does not exist.'
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-httproute-2
    namespace: default
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-2
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      failOpen: true
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: 'Wasm: secret default/my-pull-secret-1 does not exist.'
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-httproute-3
    namespace: default
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-3
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      failOpen: true
      name: wasm-filter-1
      rootID: my-root-id
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-2
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-2:v1.0.0
        type: Image
      failOpen: false
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: |-
          Wasm: secret default/my-pull-secret-1 does not exist
          secret default/my-pull-secret-2 does not exist.
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-httproute-4
    namespace: default
  spec:
    lua:
    - inline: function envoy_on_response(response_handle) response_handle:UnknownApi()
        end
      type: Inline
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-4
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      failOpen: true
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: "Wasm: secret default/my-pull-secret-1 does not exist\nLua: validation
          failed for lua body in policy with name envoyextensionpolicy/default/policy-for-httproute-4/lua/0:
          failed to validate with envoy_on_response: <string>:614: attempt to call
          a non-function object\nstack traceback:\n\t<string>:614: in function 'envoy_on_response'\n\t<string>:615:
          in main chunk\n\t[G]: ?."
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway-2
    namespace: default
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-2
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      failOpen: true
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-2
        namespace: default
      conditions:
      - lastTransitionTime: null
        message: 'Wasm: secret default/my-pull-secret-1 does not exist.'
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway-3
    namespace: default
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-3
    wasm:
    - code:
        image:
          pullSecretRef:
            group: null
            kind: null
            name: my-pull-secret-1
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
          url: oci://www.example.com/wasm-filter-1:v1.0.0
        type: Image
      name: wasm-filter-1
      rootID: my-root-id
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-3
        namespace: default
      conditions:
      - lastTransitionTime: null
        message: 'Wasm: secret default/my-pull-secret-1 does not exist.'
        reason: Invalid
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: default
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 4
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-2
    namespace: default
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 2
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-3
    namespace: default
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - www.foo.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /foo
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    hostnames:
    - www.bar.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-3
    namespace: default
  spec:
    hostnames:
    - www.baz.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /baz
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-4
    namespace: default
  spec:
    hostnames:
    - www.qux.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /qux
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-5
    namespace: default
  spec:
    hostnames:
    - www.foo.com
    parentRefs:
    - name: gateway-2
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /foo
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-2
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-6
    namespace: default
  spec:
    hostnames:
    - www.bar.com
    parentRefs:
    - name: gateway-2
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-2
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-6
    namespace: default
  spec:
    hostnames:
    - www.bar.com
    parentRefs:
    - name: gateway-3
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-3
        namespace: default
        sectionName: http
infraIR:
  default/gateway-1:
    proxy:
      listeners:
      - address: null
        name: default/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: default
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: default/gateway-1
      namespace: envoy-gateway-system
  default/gateway-2:
    proxy:
      listeners:
      - address: null
        name: default/gateway-2/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-2
          gateway.envoyproxy.io/owning-gateway-namespace: default
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: default/gateway-2
      namespace: envoy-gateway-system
  default/gateway-3:
    proxy:
      listeners:
      - address: null
        name: default/gateway-3/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-3
          gateway.envoyproxy.io/owning-gateway-namespace: default
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: default/gateway-3
      namespace: envoy-gateway-system
xdsIR:
  default/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      name: default/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        directResponse:
          statusCode: 500
        hostname: www.foo.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/www_foo_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.bar.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/www_bar_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-3
            namespace: default
          name: httproute/default/httproute-3/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-3/rule/0/backend/0
            protocol: HTTP
            weight: 1
        directResponse:
          statusCode: 500
        hostname: www.baz.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-3
          namespace: default
        name: httproute/default/httproute-3/rule/0/match/0/www_baz_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /baz
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-4
            namespace: default
          name: httproute/default/httproute-4/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-4/rule/0/backend/0
            protocol: HTTP
            weight: 1
        directResponse:
          statusCode: 500
        hostname: www.qux.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-4
          namespace: default
        name: httproute/default/httproute-4/rule/0/match/0/www_qux_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /qux
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  default/gateway-2:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: default
        sectionName: http
      name: default/gateway-2/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-5
            namespace: default
          name: httproute/default/httproute-5/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-5/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.foo.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-5
          namespace: default
        name: httproute/default/httproute-5/rule/0/match/0/www_foo_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-6
            namespace: default
          name: httproute/default/httproute-6/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-6/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.bar.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-6
          namespace: default
        name: httproute/default/httproute-6/rule/0/match/0/www_bar_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  default/gateway-3:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-3
        namespace: default
        sectionName: http
      name: default/gateway-3/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-6
            namespace: default
          name: httproute/default/httproute-6/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-6/rule/0/backend/0
            protocol: HTTP
            weight: 1
        directResponse:
          statusCode: 500
        hostname: www.bar.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-6
          namespace: default
        name: httproute/default/httproute-6/rule/0/match/0/www_bar_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003

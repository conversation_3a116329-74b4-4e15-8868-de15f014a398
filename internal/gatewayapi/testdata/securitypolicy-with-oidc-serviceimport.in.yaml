secrets:
  - apiVersion: v1
    kind: Secret
    metadata:
      namespace: envoy-gateway
      name: client1-secret
    data:
      client-secret: Y2xpZW50MTpzZWNyZXQK
  - apiVersion: v1
    kind: Secret
    metadata:
      namespace: envoy-gateway-system
      name: envoy-oidc-hmac
    data:
      hmac-secret: qrOYACHXoe7UEDI/raOjNSx+Z9ufXSc/22C3T6X/zPY=
gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-1
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      namespace: default
      name: httproute-1
    spec:
      hostnames:
        - www.example.com
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-1
          sectionName: http
      rules:
        - matches:
            - path:
                value: "/foo"
          backendRefs:
            - name: service-1
              port: 8080
serviceImports:
  - apiVersion: multicluster.x-k8s.io/v1alpha1
    kind: ServiceImport
    metadata:
      name: grpc-backend
      namespace: envoy-gateway
    spec:
      ips:
        - *******
      ports:
        - port: 9000
          name: grpc
          protocol: TCP
endpointSlices:
  - apiVersion: discovery.k8s.io/v1
    kind: EndpointSlice
    metadata:
      name: endpointslice-grpc-backend
      namespace: envoy-gateway
      labels:
        multicluster.kubernetes.io/service-name: grpc-backend
    addressType: IPv4
    ports:
      - name: grpc
        protocol: TCP
        port: 9000
    endpoints:
      - addresses:
          - *******
        conditions:
          ready: true
securityPolicies:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: SecurityPolicy
    metadata:
      namespace: envoy-gateway
      name: policy-for-gateway
      uid: b8284d0f-de82-4c65-b204-96a0d3f258a1
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
      oidc:
        provider:
          backendRefs:
            - group: multicluster.x-k8s.io
              kind: ServiceImport
              name: grpc-backend
              port: 9000
          backendSettings:
            retry:
              numRetries: 3
              perRetry:
                backOff:
                  baseInterval: 1s
                  maxInterval: 5s
              retryOn:
                triggers: [ "5xx", "gateway-error", "reset" ]
          issuer: "https://oauth.foo.com"
          authorizationEndpoint: "https://oauth.foo.com/oauth2/v2/auth"
          tokenEndpoint: "https://oauth.foo.com/token"
        clientID: "client1.apps.googleusercontent.com"
        clientSecret:
          name: "client1-secret"
        redirectURL: "https://www.example.com/bar/oauth2/callback"
        logoutPath: "/bar/logout"
        forwardAccessToken: true
        defaultTokenTTL: 30m
        refreshToken: true
        defaultRefreshTokenTTL: 24h
configMaps:
  - apiVersion: v1
    kind: ConfigMap
    metadata:
      name: ca-cmap
      namespace: envoy-gateway
    data:
      ca.crt: |
        -----BEGIN CERTIFICATE-----
        MIIDJzCCAg+gAwIBAgIUAl6UKIuKmzte81cllz5PfdN2IlIwDQYJKoZIhvcNAQEL
        BQAwIzEQMA4GA1UEAwwHbXljaWVudDEPMA0GA1UECgwGa3ViZWRiMB4XDTIzMTAw
        MjA1NDE1N1oXDTI0MTAwMTA1NDE1N1owIzEQMA4GA1UEAwwHbXljaWVudDEPMA0G
        A1UECgwGa3ViZWRiMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwSTc
        1yj8HW62nynkFbXo4VXKv2jC0PM7dPVky87FweZcTKLoWQVPQE2p2kLDK6OEszmM
        yyr+xxWtyiveremrWqnKkNTYhLfYPhgQkczib7eUalmFjUbhWdLvHakbEgCodn3b
        kz57mInX2VpiDOKg4kyHfiuXWpiBqrCx0KNLpxo3DEQcFcsQTeTHzh4752GV04RU
        Ti/GEWyzIsl4Rg7tGtAwmcIPgUNUfY2Q390FGqdH4ahn+mw/6aFbW31W63d9YJVq
        ioyOVcaMIpM5B/c7Qc8SuhCI1YGhUyg4cRHLEw5VtikioyE3X04kna3jQAj54YbR
        bpEhc35apKLB21HOUQIDAQABo1MwUTAdBgNVHQ4EFgQUyvl0VI5vJVSuYFXu7B48
        6PbMEAowHwYDVR0jBBgwFoAUyvl0VI5vJVSuYFXu7B486PbMEAowDwYDVR0TAQH/
        BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAMLxrgFVMuNRq2wAwcBt7SnNR5Cfz
        2MvXq5EUmuawIUi9kaYjwdViDREGSjk7JW17vl576HjDkdfRwi4E28SydRInZf6J
        i8HZcZ7caH6DxR335fgHVzLi5NiTce/OjNBQzQ2MJXVDd8DBmG5fyatJiOJQ4bWE
        A7FlP0RdP3CO3GWE0M5iXOB2m1qWkE2eyO4UHvwTqNQLdrdAXgDQlbam9e4BG3Gg
        d/6thAkWDbt/QNT+EJHDCvhDRKh1RuGHyg+Y+/nebTWWrFWsktRrbOoHCZiCpXI1
        3eXE6nt0YkgtDxG22KqnhpAg9gUSs2hlhoxyvkzyF0mu6NhPlwAgnq7+/Q==
        -----END CERTIFICATE-----
backendTLSPolicies:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: BackendTLSPolicy
    metadata:
      name: policy-btls-grpc-backend
      namespace: envoy-gateway
    spec:
      targetRefs:
        - group: multicluster.x-k8s.io
          kind: ServiceImport
          name: grpc-backend
          sectionName: grpc
      validation:
        caCertificateRefs:
          - name: ca-cmap
            group: ''
            kind: ConfigMap
        hostname: oauth.foo.com

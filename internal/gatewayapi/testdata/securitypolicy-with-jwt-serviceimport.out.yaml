backendTLSPolicies:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: BackendTLSPolicy
  metadata:
    creationTimestamp: null
    name: policy-btls-grpc-backend
    namespace: default
  spec:
    targetRefs:
    - group: multicluster.x-k8s.io
      kind: ServiceImport
      name: grpc-backend
      sectionName: grpc
    validation:
      caCertificateRefs:
      - group: ""
        kind: ConfigMap
        name: ca-cmap
      hostname: foo.bar.com
  status:
    ancestors:
    - ancestorRef:
        group: gateway.envoyproxy.io
        kind: SecurityPolicy
        name: policy-for-route
        namespace: default
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: default
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
infraIR:
  default/gateway-1:
    proxy:
      listeners:
      - address: null
        name: default/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: default
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: default/gateway-1
      namespace: envoy-gateway-system
securityPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-route
    namespace: default
  spec:
    jwt:
      providers:
      - audiences:
        - foo.bar.com
        claimToHeaders:
        - claim: claim
          header: claim-header
        issuer: https://foo.bar.com
        name: foobar
        remoteJWKS:
          backendRefs:
          - group: multicluster.x-k8s.io
            kind: ServiceImport
            name: grpc-backend
            port: 9000
          backendSettings:
            retry:
              numRetries: 3
              perRetry:
                backOff:
                  baseInterval: 1s
                  maxInterval: 5s
              retryOn:
                triggers:
                - 5xx
                - gateway-error
                - reset
          uri: https://foo.bar.com/jwt/public-key/jwks.json
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
xdsIR:
  default/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      name: default/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /
        security:
          jwt:
            providers:
            - audiences:
              - foo.bar.com
              claimToHeaders:
              - claim: claim
                header: claim-header
              issuer: https://foo.bar.com
              name: foobar
              remoteJWKS:
                destination:
                  metadata:
                    kind: SecurityPolicy
                    name: policy-for-route
                    namespace: default
                  name: securitypolicy/default/policy-for-route/jwt/0
                  settings:
                  - addressType: IP
                    endpoints:
                    - host: *******
                      port: 9000
                    metadata:
                      kind: ServiceImport
                      name: grpc-backend
                      namespace: default
                      sectionName: "9000"
                    name: securitypolicy/default/policy-for-route/jwt/0/backend/0
                    protocol: HTTPS
                    tls:
                      alpnProtocols: null
                      caCertificate:
                        certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKekNDQWcrZ0F3SUJBZ0lVQWw2VUtJdUttenRlODFjbGx6NVBmZE4ySWxJd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0l6RVFNQTRHQTFVRUF3d0hiWGxqYVdWdWRERVBNQTBHQTFVRUNnd0dhM1ZpWldSaU1CNFhEVEl6TVRBdwpNakExTkRFMU4xb1hEVEkwTVRBd01UQTFOREUxTjFvd0l6RVFNQTRHQTFVRUF3d0hiWGxqYVdWdWRERVBNQTBHCkExVUVDZ3dHYTNWaVpXUmlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXdTVGMKMXlqOEhXNjJueW5rRmJYbzRWWEt2MmpDMFBNN2RQVmt5ODdGd2VaY1RLTG9XUVZQUUUycDJrTERLNk9Fc3ptTQp5eXIreHhXdHlpdmVyZW1yV3FuS2tOVFloTGZZUGhnUWtjemliN2VVYWxtRmpVYmhXZEx2SGFrYkVnQ29kbjNiCmt6NTdtSW5YMlZwaURPS2c0a3lIZml1WFdwaUJxckN4MEtOTHB4bzNERVFjRmNzUVRlVEh6aDQ3NTJHVjA0UlUKVGkvR0VXeXpJc2w0Umc3dEd0QXdtY0lQZ1VOVWZZMlEzOTBGR3FkSDRhaG4rbXcvNmFGYlczMVc2M2Q5WUpWcQppb3lPVmNhTUlwTTVCL2M3UWM4U3VoQ0kxWUdoVXlnNGNSSExFdzVWdGlraW95RTNYMDRrbmEzalFBajU0WWJSCmJwRWhjMzVhcEtMQjIxSE9VUUlEQVFBQm8xTXdVVEFkQmdOVkhRNEVGZ1FVeXZsMFZJNXZKVlN1WUZYdTdCNDgKNlBiTUVBb3dId1lEVlIwakJCZ3dGb0FVeXZsMFZJNXZKVlN1WUZYdTdCNDg2UGJNRUFvd0R3WURWUjBUQVFILwpCQVV3QXdFQi96QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFNTHhyZ0ZWTXVOUnEyd0F3Y0J0N1NuTlI1Q2Z6CjJNdlhxNUVVbXVhd0lVaTlrYVlqd2RWaURSRUdTams3SlcxN3ZsNTc2SGpEa2RmUndpNEUyOFN5ZFJJblpmNkoKaThIWmNaN2NhSDZEeFIzMzVmZ0hWekxpNU5pVGNlL09qTkJRelEyTUpYVkRkOERCbUc1ZnlhdEppT0pRNGJXRQpBN0ZsUDBSZFAzQ08zR1dFME01aVhPQjJtMXFXa0UyZXlPNFVIdndUcU5RTGRyZEFYZ0RRbGJhbTllNEJHM0dnCmQvNnRoQWtXRGJ0L1FOVCtFSkhEQ3ZoRFJLaDFSdUdIeWcrWSsvbmViVFdXckZXc2t0UnJiT29IQ1ppQ3BYSTEKM2VYRTZudDBZa2d0RHhHMjJLcW5ocEFnOWdVU3MyaGxob3h5dmt6eUYwbXU2TmhQbHdBZ25xNysvUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
                        name: policy-btls-grpc-backend/default-ca
                      sni: foo.bar.com
                    weight: 1
                traffic:
                  retry:
                    numRetries: 3
                    perRetry:
                      backOff:
                        baseInterval: 1s
                        maxInterval: 5s
                    retryOn:
                      triggers:
                      - 5xx
                      - gateway-error
                      - reset
                uri: https://foo.bar.com/jwt/public-key/jwks.json
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003

apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: '{{ .Release.Namespace }}'
  {{- with .Values.dashboard.labels }}
  labels: {{- . | toYaml | nindent 4 }}
  {{- end }}
  {{- with .Values.dashboard.annotations }}
  annotations: {{- . | toYaml | nindent 4 }}
  {{- end }}
data:
  {{- $files := .Files }}
  {{- range $path, $_ := $files.Glob "dashboards/*.json" }}
  {{ $path | base }}: |-
    {{- $files.Get $path | nindent 6 -}}
  {{ end }}
